-- Migration: Add privacy and content moderation features
-- This migration adds GDPR compliance features and content moderation system

-- Add deletion tracking columns to users table
ALTER TABLE users 
ADD COLUMN deletion_requested_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN deletion_reason TEXT;

-- Create data deletion requests table
CREATE TABLE data_deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    reason TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES users(id),
    notes TEXT,
    CONSTRAINT data_deletion_requests_status_check CHECK (status IN ('pending', 'processing', 'completed', 'cancelled'))
);

-- <PERSON>reate data export logs table
CREATE TABLE data_export_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    export_type VARCHAR(50) NOT NULL DEFAULT 'full',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    file_size_bytes BIGINT,
    data_types JSONB
);

-- Create content moderation queue table
CREATE TABLE content_moderation_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type VARCHAR(50) NOT NULL,
    content_id VARCHAR(255) NOT NULL,
    content_text TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    moderation_result JSONB,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    priority VARCHAR(20) NOT NULL DEFAULT 'normal',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES users(id),
    admin_decision VARCHAR(50),
    admin_notes TEXT,
    CONSTRAINT content_moderation_queue_status_check CHECK (status IN ('pending', 'approved', 'rejected', 'escalated')),
    CONSTRAINT content_moderation_queue_priority_check CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
);

-- Create moderation actions log table
CREATE TABLE moderation_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    queue_item_id UUID REFERENCES content_moderation_queue(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL,
    performed_by UUID REFERENCES users(id),
    reason TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT moderation_actions_action_type_check CHECK (action_type IN ('auto_approve', 'auto_reject', 'manual_approve', 'manual_reject', 'escalate', 'flag'))
);

-- Create indexes for performance
CREATE INDEX idx_data_deletion_requests_user_id ON data_deletion_requests(user_id);
CREATE INDEX idx_data_deletion_requests_status ON data_deletion_requests(status);
CREATE INDEX idx_data_deletion_requests_created_at ON data_deletion_requests(created_at);
CREATE INDEX idx_users_deletion_requested_at ON users(deletion_requested_at);

CREATE INDEX idx_data_export_logs_user_id ON data_export_logs(user_id);
CREATE INDEX idx_data_export_logs_created_at ON data_export_logs(created_at);

CREATE INDEX idx_content_moderation_queue_status ON content_moderation_queue(status);
CREATE INDEX idx_content_moderation_queue_priority ON content_moderation_queue(priority);
CREATE INDEX idx_content_moderation_queue_created_at ON content_moderation_queue(created_at);
CREATE INDEX idx_content_moderation_queue_user_id ON content_moderation_queue(user_id);
CREATE INDEX idx_content_moderation_queue_content_type ON content_moderation_queue(content_type);

CREATE INDEX idx_moderation_actions_queue_item_id ON moderation_actions(queue_item_id);
CREATE INDEX idx_moderation_actions_performed_by ON moderation_actions(performed_by);
CREATE INDEX idx_moderation_actions_created_at ON moderation_actions(created_at);

-- Add table descriptions
COMMENT ON TABLE data_deletion_requests IS 'Tracks user requests for data deletion under GDPR Article 17 (Right to Erasure)';
COMMENT ON TABLE data_export_logs IS 'Logs user data export requests under GDPR Article 15 (Right of Access)';
COMMENT ON TABLE content_moderation_queue IS 'Queue for content requiring manual review by administrators';
COMMENT ON TABLE moderation_actions IS 'Log of all moderation actions taken on content items';

-- Add column descriptions
COMMENT ON COLUMN users.deletion_requested_at IS 'Timestamp when user requested account deletion';
COMMENT ON COLUMN users.deletion_reason IS 'User-provided reason for account deletion request';

-- Log this migration
INSERT INTO migration_log (id, migration_name, description) 
VALUES (25, '025-add-privacy-and-moderation-features', 'Add GDPR compliance features and content moderation system for legal compliance');
