/**
 * Integration layer for content moderation with existing BenefitLens features
 */

import { query } from './local-db'
import { moderateBenefitContent, needsManualReview, ModerationResult } from './content-moderation'

export interface ModerationQueueItem {
  id: string
  contentType: string
  contentId: string
  contentText: string
  userId: string
  moderationResult: ModerationResult
  priority: 'low' | 'normal' | 'high' | 'urgent'
}

/**
 * Add content to moderation queue
 */
export async function addToModerationQueue(
  contentType: string,
  contentId: string,
  contentText: string,
  userId: string,
  moderationResult: ModerationResult,
  priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'
): Promise<string> {
  const result = await query(`
    INSERT INTO content_moderation_queue (
      content_type, content_id, content_text, user_id, 
      moderation_result, priority, status
    ) VALUES ($1, $2, $3, $4, $5, $6, 'pending')
    RETURNING id
  `, [
    contentType,
    contentId,
    contentText,
    userId,
    JSON.stringify(moderationResult),
    priority
  ])

  return result.rows[0].id
}

/**
 * Log moderation action
 */
export async function logModerationAction(
  queueItemId: string,
  actionType: string,
  performedBy?: string,
  reason?: string,
  metadata?: Record<string, unknown>
): Promise<void> {
  await query(`
    INSERT INTO moderation_actions (
      queue_item_id, action_type, performed_by, reason, metadata
    ) VALUES ($1, $2, $3, $4, $5)
  `, [
    queueItemId,
    actionType,
    performedBy || null,
    reason || null,
    metadata ? JSON.stringify(metadata) : null
  ])
}

/**
 * Moderate benefit verification content
 */
export async function moderateBenefitVerification(
  verificationId: string,
  benefitName: string,
  companyName: string,
  userId: string,
  additionalContext?: string
): Promise<{ approved: boolean; queueItemId?: string }> {
  const content = `Benefit verification: ${benefitName} at ${companyName}${additionalContext ? ` - ${additionalContext}` : ''}`
  
  const moderationResult = await moderateBenefitContent(
    content,
    'benefit_verification',
    userId,
    { benefitName, companyName, verificationId }
  )

  // If content is automatically approved
  if (moderationResult.approved && !needsManualReview(moderationResult)) {
    // Auto-approve the verification
    await query(`
      UPDATE benefit_verifications 
      SET status = 'approved', moderated_at = NOW()
      WHERE id = $1
    `, [verificationId])

    // Log the auto-approval
    await logModerationAction(
      verificationId,
      'auto_approve',
      undefined,
      'Automatically approved by content moderation system',
      { moderationResult }
    )

    return { approved: true }
  }

  // If content is automatically rejected
  if (!moderationResult.approved && !needsManualReview(moderationResult)) {
    // Auto-reject the verification
    await query(`
      UPDATE benefit_verifications 
      SET status = 'rejected', moderated_at = NOW()
      WHERE id = $1
    `, [verificationId])

    // Log the auto-rejection
    await logModerationAction(
      verificationId,
      'auto_reject',
      undefined,
      moderationResult.reason || 'Automatically rejected by content moderation system',
      { moderationResult }
    )

    return { approved: false }
  }

  // Content needs manual review
  const priority = moderationResult.confidence < 0.3 ? 'high' : 'normal'
  const queueItemId = await addToModerationQueue(
    'benefit_verification',
    verificationId,
    content,
    userId,
    moderationResult,
    priority
  )

  // Set verification status to pending review
  await query(`
    UPDATE benefit_verifications 
    SET status = 'pending_review'
    WHERE id = $1
  `, [verificationId])

  // Log the manual review requirement
  await logModerationAction(
    queueItemId,
    'flag',
    undefined,
    'Flagged for manual review',
    { moderationResult }
  )

  return { approved: false, queueItemId }
}

/**
 * Moderate benefit dispute content
 */
export async function moderateBenefitDispute(
  disputeId: string,
  benefitName: string,
  companyName: string,
  disputeReason: string,
  userId: string
): Promise<{ approved: boolean; queueItemId?: string }> {
  const content = `Benefit dispute: ${benefitName} at ${companyName} - Reason: ${disputeReason}`
  
  const moderationResult = await moderateBenefitContent(
    content,
    'benefit_dispute',
    userId,
    { benefitName, companyName, disputeReason, disputeId }
  )

  // If content is automatically approved
  if (moderationResult.approved && !needsManualReview(moderationResult)) {
    await query(`
      UPDATE benefit_removal_disputes 
      SET status = 'approved', moderated_at = NOW()
      WHERE id = $1
    `, [disputeId])

    await logModerationAction(
      disputeId,
      'auto_approve',
      undefined,
      'Automatically approved by content moderation system',
      { moderationResult }
    )

    return { approved: true }
  }

  // If content is automatically rejected
  if (!moderationResult.approved && !needsManualReview(moderationResult)) {
    await query(`
      UPDATE benefit_removal_disputes 
      SET status = 'rejected', moderated_at = NOW()
      WHERE id = $1
    `, [disputeId])

    await logModerationAction(
      disputeId,
      'auto_reject',
      undefined,
      moderationResult.reason || 'Automatically rejected by content moderation system',
      { moderationResult }
    )

    return { approved: false }
  }

  // Content needs manual review
  const priority = moderationResult.confidence < 0.3 ? 'high' : 'normal'
  const queueItemId = await addToModerationQueue(
    'benefit_dispute',
    disputeId,
    content,
    userId,
    moderationResult,
    priority
  )

  await query(`
    UPDATE benefit_removal_disputes 
    SET status = 'pending_review'
    WHERE id = $1
  `, [disputeId])

  await logModerationAction(
    queueItemId,
    'flag',
    undefined,
    'Flagged for manual review',
    { moderationResult }
  )

  return { approved: false, queueItemId }
}

/**
 * Moderate custom benefit names when adding new benefits
 */
export async function moderateCustomBenefitName(
  benefitName: string,
  userId: string
): Promise<{ approved: boolean; reason?: string }> {
  const moderationResult = await moderateBenefitContent(
    benefitName,
    'benefit_name',
    userId,
    { benefitName }
  )

  // For benefit names, we're more strict - require manual review for anything flagged
  if (!moderationResult.approved || moderationResult.flags.length > 0) {
    return {
      approved: false,
      reason: moderationResult.reason || 'Benefit name requires review'
    }
  }

  return { approved: true }
}

/**
 * Get moderation statistics for admin dashboard
 */
export async function getModerationStats(): Promise<{
  pending: number
  approved: number
  rejected: number
  escalated: number
  totalToday: number
  averageReviewTime: number
}> {
  const statsResult = await query(`
    SELECT 
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
      COUNT(CASE WHEN status = 'escalated' THEN 1 END) as escalated,
      COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as total_today,
      AVG(
        CASE 
          WHEN reviewed_at IS NOT NULL 
          THEN EXTRACT(EPOCH FROM (reviewed_at - created_at)) / 3600 
        END
      ) as avg_review_hours
    FROM content_moderation_queue
  `)

  const stats = statsResult.rows[0]
  
  return {
    pending: parseInt(stats.pending) || 0,
    approved: parseInt(stats.approved) || 0,
    rejected: parseInt(stats.rejected) || 0,
    escalated: parseInt(stats.escalated) || 0,
    totalToday: parseInt(stats.total_today) || 0,
    averageReviewTime: parseFloat(stats.avg_review_hours) || 0
  }
}
