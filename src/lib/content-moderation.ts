/**
 * Content Moderation System for BenefitLens
 * Provides automated and manual review capabilities for user-generated content
 */

export interface ModerationResult {
  approved: boolean
  confidence: number
  flags: string[]
  reason?: string
  requiresManualReview: boolean
}

export interface ContentItem {
  id: string
  type: 'benefit_verification' | 'benefit_dispute' | 'company_review' | 'benefit_name'
  content: string
  userId: string
  metadata?: Record<string, unknown>
}

export interface ModerationRule {
  name: string
  type: 'keyword' | 'pattern' | 'length' | 'custom'
  severity: 'low' | 'medium' | 'high'
  action: 'flag' | 'reject' | 'manual_review'
  config: Record<string, unknown>
}

// Predefined moderation rules
const MODERATION_RULES: ModerationRule[] = [
  // Profanity and inappropriate content
  {
    name: 'profanity_filter',
    type: 'keyword',
    severity: 'high',
    action: 'reject',
    config: {
      keywords: [
        // German profanity (basic list - would need expansion)
        'scheiße', 'verdammt', 'arschloch', 'idiot', 'blöd',
        // English profanity
        'fuck', 'shit', 'damn', 'asshole', 'stupid'
      ],
      caseSensitive: false
    }
  },
  
  // Spam detection
  {
    name: 'spam_detection',
    type: 'pattern',
    severity: 'medium',
    action: 'flag',
    config: {
      patterns: [
        /(.)\1{4,}/, // Repeated characters (aaaaa)
        /[A-Z]{5,}/, // All caps words
        /(https?:\/\/[^\s]+)/g, // URLs
        /(\b\w+\b.*){3,}\1/, // Repeated phrases
      ]
    }
  },
  
  // Content length validation
  {
    name: 'content_length',
    type: 'length',
    severity: 'low',
    action: 'flag',
    config: {
      minLength: 3,
      maxLength: 1000
    }
  },
  
  // Suspicious patterns
  {
    name: 'suspicious_patterns',
    type: 'pattern',
    severity: 'medium',
    action: 'manual_review',
    config: {
      patterns: [
        /\b(fake|falsch|lüge|betrug|scam)\b/gi, // Claims of fraud
        /\b(illegal|rechtswidrig|verboten)\b/gi, // Legal concerns
        /\b(geld|euro|€|dollar|\$)\s*\d+/gi, // Money amounts
      ]
    }
  },
  
  // Personal information detection
  {
    name: 'personal_info',
    type: 'pattern',
    severity: 'high',
    action: 'manual_review',
    config: {
      patterns: [
        /\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/, // Credit card numbers
        /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email addresses
        /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/, // Phone numbers
        /\b\d{5}\s+[A-Za-z\s]+\b/, // German postal codes + cities
      ]
    }
  }
]

export class ContentModerator {
  private rules: ModerationRule[]
  
  constructor(customRules: ModerationRule[] = []) {
    this.rules = [...MODERATION_RULES, ...customRules]
  }
  
  /**
   * Moderate content and return moderation result
   */
  async moderateContent(content: ContentItem): Promise<ModerationResult> {
    const result: ModerationResult = {
      approved: true,
      confidence: 1.0,
      flags: [],
      requiresManualReview: false
    }
    
    // Apply each rule
    for (const rule of this.rules) {
      const ruleResult = this.applyRule(rule, content.content)
      
      if (ruleResult.triggered) {
        result.flags.push(`${rule.name}: ${ruleResult.reason}`)
        result.confidence *= (1 - ruleResult.severity)
        
        // Determine action based on rule
        switch (rule.action) {
          case 'reject':
            result.approved = false
            result.reason = `Content rejected: ${ruleResult.reason}`
            break
            
          case 'manual_review':
            result.requiresManualReview = true
            break
            
          case 'flag':
            // Just flag for review but don't reject
            break
        }
        
        // High severity rules with low confidence should require manual review
        if (rule.severity === 'high' && result.confidence < 0.7) {
          result.requiresManualReview = true
        }
      }
    }
    
    // If confidence is very low, require manual review
    if (result.confidence < 0.5) {
      result.requiresManualReview = true
    }
    
    return result
  }
  
  /**
   * Apply a single moderation rule
   */
  private applyRule(rule: ModerationRule, content: string): { triggered: boolean; reason?: string; severity: number } {
    const severityMap = { low: 0.1, medium: 0.3, high: 0.6 }
    
    switch (rule.type) {
      case 'keyword':
        return this.applyKeywordRule(rule, content, severityMap[rule.severity])
        
      case 'pattern':
        return this.applyPatternRule(rule, content, severityMap[rule.severity])
        
      case 'length':
        return this.applyLengthRule(rule, content, severityMap[rule.severity])
        
      default:
        return { triggered: false, severity: 0 }
    }
  }
  
  private applyKeywordRule(rule: ModerationRule, content: string, severity: number) {
    const keywords = rule.config.keywords as string[]
    const caseSensitive = rule.config.caseSensitive as boolean
    const searchContent = caseSensitive ? content : content.toLowerCase()
    
    for (const keyword of keywords) {
      const searchKeyword = caseSensitive ? keyword : keyword.toLowerCase()
      if (searchContent.includes(searchKeyword)) {
        return {
          triggered: true,
          reason: `Contains inappropriate keyword: ${keyword}`,
          severity
        }
      }
    }
    
    return { triggered: false, severity: 0 }
  }
  
  private applyPatternRule(rule: ModerationRule, content: string, severity: number) {
    const patterns = rule.config.patterns as RegExp[]
    
    for (const pattern of patterns) {
      if (pattern.test(content)) {
        return {
          triggered: true,
          reason: `Matches suspicious pattern: ${pattern.source}`,
          severity
        }
      }
    }
    
    return { triggered: false, severity: 0 }
  }
  
  private applyLengthRule(rule: ModerationRule, content: string, severity: number) {
    const minLength = rule.config.minLength as number
    const maxLength = rule.config.maxLength as number
    
    if (content.length < minLength) {
      return {
        triggered: true,
        reason: `Content too short (${content.length} < ${minLength})`,
        severity
      }
    }
    
    if (content.length > maxLength) {
      return {
        triggered: true,
        reason: `Content too long (${content.length} > ${maxLength})`,
        severity
      }
    }
    
    return { triggered: false, severity: 0 }
  }
  
  /**
   * Get moderation statistics
   */
  getStats() {
    return {
      totalRules: this.rules.length,
      rulesByType: this.rules.reduce((acc, rule) => {
        acc[rule.type] = (acc[rule.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      rulesBySeverity: this.rules.reduce((acc, rule) => {
        acc[rule.severity] = (acc[rule.severity] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }
  }
}

// Export singleton instance
export const contentModerator = new ContentModerator()

/**
 * Utility function to moderate benefit-related content
 */
export async function moderateBenefitContent(
  content: string,
  type: ContentItem['type'],
  userId: string,
  metadata?: Record<string, unknown>
): Promise<ModerationResult> {
  const contentItem: ContentItem = {
    id: `${type}_${Date.now()}_${Math.random().toString(36).substring(2)}`,
    type,
    content,
    userId,
    metadata
  }
  
  return contentModerator.moderateContent(contentItem)
}

/**
 * Check if content needs manual review based on moderation result
 */
export function needsManualReview(result: ModerationResult): boolean {
  return result.requiresManualReview || 
         !result.approved || 
         result.confidence < 0.7 ||
         result.flags.length > 2
}
