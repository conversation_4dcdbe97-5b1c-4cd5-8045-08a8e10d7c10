import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

// PATCH /api/admin/content-moderation/[id] - Review content moderation item
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await requireAdmin()
    const { id } = await params
    const body = await request.json()
    const { decision, notes, reason } = body

    if (!decision || !['approved', 'rejected', 'escalated'].includes(decision)) {
      return NextResponse.json(
        { error: 'Invalid decision. Must be: approved, rejected, or escalated' },
        { status: 400 }
      )
    }

    // Get the moderation item
    const itemResult = await query(`
      SELECT * FROM content_moderation_queue WHERE id = $1
    `, [id])

    if (itemResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Moderation item not found' },
        { status: 404 }
      )
    }

    const item = itemResult.rows[0]

    // Update the moderation item
    await query(`
      UPDATE content_moderation_queue 
      SET 
        status = $1,
        reviewed_at = NOW(),
        reviewed_by = $2,
        admin_decision = $1,
        admin_notes = $3
      WHERE id = $4
    `, [decision, admin.id, notes, id])

    // Log the moderation action
    await query(`
      INSERT INTO moderation_actions (
        queue_item_id, action_type, performed_by, reason, metadata
      ) VALUES ($1, $2, $3, $4, $5)
    `, [
      id,
      `manual_${decision}`,
      admin.id,
      reason || notes,
      JSON.stringify({
        originalStatus: item.status,
        moderationResult: item.moderation_result,
        adminDecision: decision
      })
    ])

    // If approved, we might need to take action on the original content
    if (decision === 'approved') {
      await handleApprovedContent(item)
    } else if (decision === 'rejected') {
      await handleRejectedContent(item)
    }

    return NextResponse.json({
      success: true,
      message: `Content ${decision} successfully`,
      item: {
        id,
        status: decision,
        reviewedBy: admin.email,
        reviewedAt: new Date().toISOString(),
        adminNotes: notes
      }
    })

  } catch (error) {
    console.error('Error reviewing moderation item:', error)
    return NextResponse.json(
      { error: 'Failed to review moderation item' },
      { status: 500 }
    )
  }
}

// GET /api/admin/content-moderation/[id] - Get specific moderation item
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()
    const { id } = await params

    // Get moderation item with related data
    const result = await query(`
      SELECT 
        cmq.*,
        u.email as user_email,
        u.first_name,
        u.last_name,
        reviewer.email as reviewer_email,
        reviewer.first_name as reviewer_first_name,
        reviewer.last_name as reviewer_last_name
      FROM content_moderation_queue cmq
      LEFT JOIN users u ON cmq.user_id = u.id
      LEFT JOIN users reviewer ON cmq.reviewed_by = reviewer.id
      WHERE cmq.id = $1
    `, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Moderation item not found' },
        { status: 404 }
      )
    }

    // Get moderation actions history
    const actionsResult = await query(`
      SELECT 
        ma.*,
        u.email as performed_by_email
      FROM moderation_actions ma
      LEFT JOIN users u ON ma.performed_by = u.id
      WHERE ma.queue_item_id = $1
      ORDER BY ma.created_at DESC
    `, [id])

    return NextResponse.json({
      item: result.rows[0],
      actions: actionsResult.rows
    })

  } catch (error) {
    console.error('Error fetching moderation item:', error)
    return NextResponse.json(
      { error: 'Failed to fetch moderation item' },
      { status: 500 }
    )
  }
}

// Helper function to handle approved content
async function handleApprovedContent(item: any) {
  try {
    switch (item.content_type) {
      case 'benefit_verification':
        // Approve the benefit verification
        await query(`
          UPDATE benefit_verifications 
          SET status = 'approved', moderated_at = NOW()
          WHERE id = $1
        `, [item.content_id])
        break

      case 'benefit_dispute':
        // Approve the benefit dispute
        await query(`
          UPDATE benefit_removal_disputes 
          SET status = 'approved', moderated_at = NOW()
          WHERE id = $1
        `, [item.content_id])
        break

      case 'company_review':
        // Approve company review (if implemented)
        break

      default:
        console.log(`No handler for approved content type: ${item.content_type}`)
    }
  } catch (error) {
    console.error('Error handling approved content:', error)
  }
}

// Helper function to handle rejected content
async function handleRejectedContent(item: any) {
  try {
    switch (item.content_type) {
      case 'benefit_verification':
        // Reject the benefit verification
        await query(`
          UPDATE benefit_verifications 
          SET status = 'rejected', moderated_at = NOW()
          WHERE id = $1
        `, [item.content_id])
        break

      case 'benefit_dispute':
        // Reject the benefit dispute
        await query(`
          UPDATE benefit_removal_disputes 
          SET status = 'rejected', moderated_at = NOW()
          WHERE id = $1
        `, [item.content_id])
        break

      case 'company_review':
        // Reject company review (if implemented)
        break

      default:
        console.log(`No handler for rejected content type: ${item.content_type}`)
    }
  } catch (error) {
    console.error('Error handling rejected content:', error)
  }
}

// DELETE /api/admin/content-moderation/[id] - Delete moderation item
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()
    const { id } = await params

    // Delete moderation actions first (foreign key constraint)
    await query(`DELETE FROM moderation_actions WHERE queue_item_id = $1`, [id])
    
    // Delete the moderation item
    const result = await query(`
      DELETE FROM content_moderation_queue WHERE id = $1 RETURNING *
    `, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Moderation item not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Moderation item deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting moderation item:', error)
    return NextResponse.json(
      { error: 'Failed to delete moderation item' },
      { status: 500 }
    )
  }
}
