import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/admin/content-moderation - Get moderation queue
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'pending'
    const priority = searchParams.get('priority')
    const contentType = searchParams.get('content_type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build query conditions
    const conditions = ['status = $1']
    const params = [status]
    let paramIndex = 2

    if (priority) {
      conditions.push(`priority = $${paramIndex}`)
      params.push(priority)
      paramIndex++
    }

    if (contentType) {
      conditions.push(`content_type = $${paramIndex}`)
      params.push(contentType)
      paramIndex++
    }

    // Get moderation queue items
    const queueQuery = `
      SELECT 
        cmq.*,
        u.email as user_email,
        u.first_name,
        u.last_name,
        reviewer.email as reviewer_email
      FROM content_moderation_queue cmq
      LEFT JOIN users u ON cmq.user_id = u.id
      LEFT JOIN users reviewer ON cmq.reviewed_by = reviewer.id
      WHERE ${conditions.join(' AND ')}
      ORDER BY 
        CASE cmq.priority 
          WHEN 'urgent' THEN 1
          WHEN 'high' THEN 2
          WHEN 'normal' THEN 3
          WHEN 'low' THEN 4
        END,
        cmq.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    const queueResult = await query(queueQuery, [...params, limit, offset])

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM content_moderation_queue
      WHERE ${conditions.join(' AND ')}
    `
    const countResult = await query(countQuery, params.slice(0, -2)) // Remove limit/offset params

    // Get summary statistics
    const statsQuery = `
      SELECT 
        status,
        priority,
        content_type,
        COUNT(*) as count
      FROM content_moderation_queue
      GROUP BY status, priority, content_type
      ORDER BY status, priority, content_type
    `
    const statsResult = await query(statsQuery)

    return NextResponse.json({
      items: queueResult.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(parseInt(countResult.rows[0].total) / limit)
      },
      statistics: statsResult.rows
    })

  } catch (error) {
    console.error('Error fetching moderation queue:', error)
    return NextResponse.json(
      { error: 'Failed to fetch moderation queue' },
      { status: 500 }
    )
  }
}

// POST /api/admin/content-moderation - Add content to moderation queue
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { 
      contentType, 
      contentId, 
      contentText, 
      userId, 
      moderationResult, 
      priority = 'normal' 
    } = body

    if (!contentType || !contentId || !contentText) {
      return NextResponse.json(
        { error: 'Missing required fields: contentType, contentId, contentText' },
        { status: 400 }
      )
    }

    // Insert into moderation queue
    const result = await query(`
      INSERT INTO content_moderation_queue (
        content_type, content_id, content_text, user_id, 
        moderation_result, priority, status
      ) VALUES ($1, $2, $3, $4, $5, $6, 'pending')
      RETURNING *
    `, [
      contentType,
      contentId,
      contentText,
      userId,
      moderationResult ? JSON.stringify(moderationResult) : null,
      priority
    ])

    return NextResponse.json({
      success: true,
      item: result.rows[0]
    })

  } catch (error) {
    console.error('Error adding to moderation queue:', error)
    return NextResponse.json(
      { error: 'Failed to add to moderation queue' },
      { status: 500 }
    )
  }
}
